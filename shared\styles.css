* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    box-shadow: 0 0 30px rgba(0,0,0,0.1);
    min-height: 100vh;
}

.header {
    background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header .subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
}

.nav {
    background: #34495e;
    padding: 1rem;
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav ul {
    list-style: none;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
}

.nav li {
    margin: 0 1rem;
}

.nav a {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    transition: background 0.3s;
}

.nav a:hover {
    background: #3498db;
}

.content {
    padding: 2rem;
}

.section {
    margin-bottom: 3rem;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    background: white;
}

.section h2 {
    color: #2c3e50;
    border-bottom: 3px solid #3498db;
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem;
    font-size: 2rem;
}

.section h3 {
    color: #34495e;
    margin: 1.5rem 0 1rem 0;
    font-size: 1.4rem;
}

.executive-summary {
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f8ff 100%);
    border-left: 5px solid #27ae60;
}

.recommendation-box {
    background: #d4edda;
    border: 2px solid #27ae60;
    border-radius: 10px;
    padding: 1.5rem;
    margin: 1rem 0;
    text-align: center;
}

.recommendation-box h3 {
    color: #155724;
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
}

.chart-container {
    position: relative;
    height: 400px;
    margin: 2rem 0;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1rem;
}

.chart-small {
    height: 300px;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.metric-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.metric-value {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.metric-label {
    font-size: 1rem;
    opacity: 0.9;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    margin: 1.5rem 0;
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.data-table th {
    background: #3498db;
    color: white;
    padding: 1rem;
    text-align: left;
    font-weight: 600;
}

.data-table td {
    padding: 1rem;
    border-bottom: 1px solid #ecf0f1;
}

.data-table tr:nth-child(even) {
    background: #f8f9fa;
}

.data-table tr:hover {
    background: #e3f2fd;
}

.risk-matrix {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin: 2rem 0;
}

.risk-item {
    padding: 1rem;
    border-radius: 8px;
    text-align: center;
    color: white;
    font-weight: bold;
}

.risk-high { background: #e74c3c; }
.risk-medium { background: #f39c12; }
.risk-low { background: #27ae60; }

.risk-category {
    padding: 1.5rem;
    border-radius: 10px;
    margin-bottom: 1rem;
}

.high-risk {
    background: #ffe8e8;
    border: 2px solid #e74c3c;
}

.medium-risk {
    background: #fff3cd;
    border: 2px solid #f39c12;
}

.low-risk {
    background: #e8f5e8;
    border: 2px solid #27ae60;
}

.scenario-card {
    background: #f8f9fa;
    border: 2px solid #3498db;
    border-radius: 10px;
    padding: 1.5rem;
    text-align: center;
}

.scenario-metric {
    font-size: 1.5rem;
    font-weight: bold;
    color: #3498db;
    margin: 1rem 0;
}

.scenario-card.conservative {
    border-color: #e74c3c;
    background: linear-gradient(135deg, #fff 0%, #ffeaea 100%);
}

.scenario-card.moderate {
    border-color: #f39c12;
    background: linear-gradient(135deg, #fff 0%, #fff8e1 100%);
}

.scenario-card.aggressive {
    border-color: #27ae60;
    background: linear-gradient(135deg, #fff 0%, #eafaf1 100%);
}

.conservative {
    border-color: #27ae60;
}

.moderate {
    border-color: #f39c12;
}

.aggressive {
    border-color: #e74c3c;
}

.insight-box {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-left: 3px solid #3498db;
}

.insight-box h4 {
    margin-top: 0;
    color: #2c3e50;
    font-size: 1.1rem;
}

.insight-box ul {
    margin: 1rem 0 0 0;
    padding-left: 1.2rem;
}

.insight-box li {
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.timeline {
    position: relative;
    margin: 2rem 0;
}

.timeline-item {
    display: flex;
    margin-bottom: 2rem;
    align-items: center;
}

.timeline-marker {
    width: 20px;
    height: 20px;
    background: #3498db;
    border-radius: 50%;
    margin-right: 1rem;
    flex-shrink: 0;
}

.timeline-content {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    flex-grow: 1;
    border-left: 3px solid #3498db;
}

.progress-bar {
    background: #ecf0f1;
    border-radius: 10px;
    height: 20px;
    margin: 1rem 0;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #27ae60, #2ecc71);
    border-radius: 10px;
    transition: width 0.3s ease;
}

.footer {
    background: #2c3e50;
    color: white;
    text-align: center;
    padding: 2rem;
    margin-top: 3rem;
}

@media (max-width: 768px) {
    .header h1 { font-size: 2rem; }
    .nav ul { flex-direction: column; }
    .nav li { margin: 0.25rem 0; }
    .content { padding: 1rem; }
    .section { padding: 1rem; }
    .metrics-grid { grid-template-columns: 1fr; }
}

.highlight {
    background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
    border-left: 4px solid #3498db;
}

.warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
}

.description-box {
    background: #f8f9fa;
    border-left: 4px solid #3498db;
    padding: 1rem;
    margin: 1rem 0;
    border-radius: 5px;
    font-style: italic;
    color: #555;
}

.description-box .desc-title {
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-style: normal;
}
