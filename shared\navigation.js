// Navigation component for AfriSure Report sections
function createNavigation(currentPage = '') {
    const navItems = [
        { href: 'index.html', text: 'Overview', id: 'overview' },
        { href: 'executive-summary.html', text: 'Executive Summary', id: 'executive-summary' },
        { href: 'market-analysis.html', text: 'Market Analysis', id: 'market-analysis' },
        { href: 'financial-projections.html', text: 'Financial Model', id: 'financial-projections' },
        { href: 'risk-assessment.html', text: 'Risk Assessment', id: 'risk-assessment' },
        { href: 'implementation.html', text: 'Implementation', id: 'implementation' },
        { href: 'management-team.html', text: 'Management', id: 'management-team' },
        { href: 'regulatory-framework.html', text: 'Regulatory', id: 'regulatory-framework' },
        { href: 'recommendations.html', text: 'Recommendations', id: 'recommendations' },
        { href: 'competitive-analysis.html', text: 'Competition', id: 'competitive-analysis' },
        { href: 'investment-analysis.html', text: 'Investment Analysis', id: 'investment-analysis' }
    ];

    let navHTML = '<nav class="nav"><ul>';
    
    navItems.forEach(item => {
        const activeClass = currentPage === item.id ? ' style="background: #3498db;"' : '';
        navHTML += `<li><a href="${item.href}"${activeClass}>${item.text}</a></li>`;
    });
    
    navHTML += '</ul></nav>';
    
    return navHTML;
}

// Function to insert navigation into the page
function insertNavigation(currentPage = '') {
    const headerElement = document.querySelector('.header');
    if (headerElement) {
        headerElement.insertAdjacentHTML('afterend', createNavigation(currentPage));
    }
}

// Auto-detect current page and insert navigation
document.addEventListener('DOMContentLoaded', function() {
    const currentPath = window.location.pathname;
    const currentPage = currentPath.substring(currentPath.lastIndexOf('/') + 1, currentPath.lastIndexOf('.html'));
    insertNavigation(currentPage);
});
