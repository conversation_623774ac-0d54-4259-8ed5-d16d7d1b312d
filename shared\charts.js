// Market Growth Chart
function createMarketGrowthChart() {
    const marketGrowthCtx = document.getElementById('marketGrowthChart');
    if (!marketGrowthCtx) return;
    
    new Chart(marketGrowthCtx.getContext('2d'), {
        type: 'line',
        data: {
            labels: ['2019', '2020', '2021', '2022', '2023', '2024'],
            datasets: [{
                label: 'Premium (UGX Millions)',
                data: [600, 650, 580, 620, 707, 1700],
                borderColor: '#3498db',
                backgroundColor: 'rgba(52, 152, 219, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'Uganda Microinsurance Market Growth (2019-2024)'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Premium (UGX Millions)'
                    }
                }
            }
        }
    });
}

// Revenue Channel Chart
function createRevenueChannelChart() {
    const revenueChannelCtx = document.getElementById('revenueChannelChart');
    if (!revenueChannelCtx) return;
    
    new Chart(revenueChannelCtx.getContext('2d'), {
        type: 'doughnut',
        data: {
            labels: ['SACCOs', 'MFI Borrowers', 'VSLAs', 'Others'],
            datasets: [{
                data: [14000, 4000, 5000, 2000],
                backgroundColor: ['#3498db', '#e74c3c', '#f39c12', '#27ae60'],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'Revenue Potential by Channel (UGX Millions)'
                },
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// Financial Projection Chart
function createFinancialProjectionChart() {
    const financialCtx = document.getElementById('financialProjectionChart');
    if (!financialCtx) return;
    
    new Chart(financialCtx.getContext('2d'), {
        type: 'bar',
        data: {
            labels: ['Year 1', 'Year 2', 'Year 3', 'Year 4', 'Year 5'],
            datasets: [
                {
                    label: 'Gross Premium Income',
                    data: [300, 450, 675, 1013, 1519],
                    backgroundColor: '#3498db'
                },
                {
                    label: 'Claims Paid',
                    data: [45, 68, 101, 152, 228],
                    backgroundColor: '#e74c3c'
                },
                {
                    label: 'Total Expenses',
                    data: [507, 493, 664, 959, 1401],
                    backgroundColor: '#f39c12'
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: '5-Year Financial Projections (UGX Millions)'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Amount (UGX Millions)'
                    }
                }
            }
        }
    });
}

// Customer Growth Chart
function createCustomerGrowthChart() {
    const customerCtx = document.getElementById('customerGrowthChart');
    if (!customerCtx) return;
    
    new Chart(customerCtx.getContext('2d'), {
        type: 'line',
        data: {
            labels: ['Year 1', 'Year 2', 'Year 3', 'Year 4', 'Year 5'],
            datasets: [{
                label: 'Number of Policies',
                data: [5000, 7500, 11250, 16875, 25313],
                borderColor: '#27ae60',
                backgroundColor: 'rgba(39, 174, 96, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'Policy Growth Projection'
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// Breakeven Analysis Chart
function createBreakevenChart() {
    const breakevenCtx = document.getElementById('breakevenChart');
    if (!breakevenCtx) return;
    
    new Chart(breakevenCtx.getContext('2d'), {
        type: 'line',
        data: {
            labels: ['Year 1', 'Year 2', 'Year 3', 'Year 4', 'Year 5'],
            datasets: [
                {
                    label: 'Gross Premium Income',
                    data: [300, 450, 675, 1013, 1519],
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    borderWidth: 3,
                    fill: false
                },
                {
                    label: 'Total Expenses',
                    data: [507, 493, 664, 959, 1401],
                    borderColor: '#e74c3c',
                    backgroundColor: 'rgba(231, 76, 60, 0.1)',
                    borderWidth: 3,
                    fill: false
                },
                {
                    label: 'Net Income',
                    data: [-207, -43, 11, 54, 118],
                    borderColor: '#27ae60',
                    backgroundColor: 'rgba(39, 174, 96, 0.1)',
                    borderWidth: 3,
                    fill: false
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'Breakeven Analysis - Revenue vs Expenses (UGX Millions)'
                }
            },
            scales: {
                y: {
                    beginAtZero: false,
                    title: {
                        display: true,
                        text: 'Amount (UGX Millions)'
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });
}

// Investment Returns Chart
function createInvestmentReturnsChart() {
    const investmentReturnsCtx = document.getElementById('investmentReturnsChart');
    if (!investmentReturnsCtx) return;
    
    new Chart(investmentReturnsCtx.getContext('2d'), {
        type: 'bar',
        data: {
            labels: ['Year 1', 'Year 2', 'Year 3', 'Year 4', 'Year 5'],
            datasets: [
                {
                    label: 'Cumulative Investment',
                    data: [500, 750, 1000, 1250, 1500],
                    backgroundColor: '#e74c3c',
                    borderColor: '#c0392b',
                    borderWidth: 1
                },
                {
                    label: 'Cumulative Returns',
                    data: [-207, -250, -239, -185, -67],
                    backgroundColor: '#27ae60',
                    borderColor: '#229954',
                    borderWidth: 1
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'Investment vs Cumulative Returns (UGX Millions)'
                }
            },
            scales: {
                y: {
                    beginAtZero: false,
                    title: {
                        display: true,
                        text: 'Amount (UGX Millions)'
                    }
                }
            }
        }
    });
}

// Scenario Analysis Chart
function createScenarioChart() {
    const scenarioCtx = document.getElementById('scenarioChart');
    if (!scenarioCtx) return;

    new Chart(scenarioCtx.getContext('2d'), {
        type: 'bar',
        data: {
            labels: ['Optimistic', 'Base Case', 'Conservative'],
            datasets: [{
                label: 'Probability (%)',
                data: [25, 50, 25],
                backgroundColor: ['#27ae60', '#3498db', '#f39c12']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'Success Probability by Scenario'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 60
                }
            }
        }
    });
}

// ROI Timeline Chart
function createROIChart() {
    const roiCtx = document.getElementById('roiChart');
    if (!roiCtx) return;

    new Chart(roiCtx.getContext('2d'), {
        type: 'line',
        data: {
            labels: ['Year 1', 'Year 2', 'Year 3', 'Year 4', 'Year 5', 'Year 6', 'Year 7'],
            datasets: [{
                label: 'ROI (%)',
                data: [-40, -22, -10, -3, 6, 20, 30],
                borderColor: '#e74c3c',
                backgroundColor: 'rgba(231, 76, 60, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'Expected ROI Timeline (%)'
                }
            },
            scales: {
                y: {
                    beginAtZero: false,
                    title: {
                        display: true,
                        text: 'ROI (%)'
                    }
                }
            }
        }
    });
}

// Market Share Chart
function createMarketShareChart() {
    const marketShareCtx = document.getElementById('marketShareChart');
    if (!marketShareCtx) return;

    new Chart(marketShareCtx.getContext('2d'), {
        type: 'pie',
        data: {
            labels: ['UAP Insurance', 'ICEA LION', 'Jubilee', 'AIG Uganda', 'Others', 'AfriSure (Target)'],
            datasets: [{
                data: [35, 25, 20, 15, 3, 2],
                backgroundColor: ['#3498db', '#e74c3c', '#f39c12', '#27ae60', '#9b59b6', '#e67e22']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'Market Share Distribution (%)'
                },
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// Initialize all charts when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    createMarketGrowthChart();
    createRevenueChannelChart();
    createFinancialProjectionChart();
    createCustomerGrowthChart();
    createBreakevenChart();
    createInvestmentReturnsChart();
    createScenarioChart();
    createROIChart();
    createMarketShareChart();
});
